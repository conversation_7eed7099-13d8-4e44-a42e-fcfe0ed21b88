package com.ets.delivery.application.app.disposer;

import com.ets.common.queue.BaseDisposer;
import com.ets.delivery.application.app.business.serial.SerialBusiness;
import com.ets.delivery.application.common.bo.serial.SerialProcessAsyncBO;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 序列号异步处理器
 * 用于异步处理序列号的入库、出库等操作
 */
@Slf4j
@NoArgsConstructor
@Component(value = "SerialProcessJobBean")
public class SerialProcessDisposer extends BaseDisposer {

    @Autowired
    private SerialBusiness serialBusiness;

    public SerialProcessDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "SerialProcessJobBean";
    }

    @Override
    public void execute(Object content) throws Exception {
        SerialProcessAsyncBO processBO = super.getParamsObject(content, SerialProcessAsyncBO.class);

        try {
            // 调用序列号处理业务逻辑
            serialBusiness.processSerials(processBO.getSerialType(), processBO.getProcessBO());
        } catch (Exception e) {
            log.error("处理序列号失败，业务单号：{}，序列号：{}，错误：{}", processBO.getProcessBO().getBusinessSn(), processBO.getProcessBO().getSerialList(), e.getMessage());
        }
    }
}
