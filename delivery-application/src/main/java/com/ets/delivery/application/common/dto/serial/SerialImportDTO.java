package com.ets.delivery.application.common.dto.serial;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 序列号导入DTO
 */
@Data
public class SerialImportDTO {

    /**
     * 序列号
     */
    @ExcelProperty(value = "序列号", index = 0)
    private String serialNo;

    /**
     * 业务单号（发货单号、入库单号等）
     */
    @ExcelProperty(value = "业务单号", index = 1)
    private String businessSn;

    /**
     * 仓库SKU编码
     */
    @ExcelProperty(value = "仓库SKU编码", index = 2)
    private String storageSku;

    /**
     * 库存类型（ZP/CC/JS/XS等）
     */
    @ExcelProperty(value = "库存类型", index = 3)
    private String inventoryType;

    /**
     * 仓库编码
     */
    @ExcelProperty(value = "仓库编码", index = 4)
    private String storageCode;

    /**
     * 业务来源
     */
    @ExcelProperty(value = "业务来源", index = 5)
    private String businessSource;

    /**
     * 序列号类型（RETURN/STOCK_IN/STOCK_OUT/DELIVERY）
     */
    @ExcelProperty(value = "序列号类型", index = 6)
    private String serialType;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注", index = 7)
    private String remark;

    /**
     * 操作时间
     */
    @ExcelProperty(value = "操作时间", index = 8)
    private String operateTime;
}