package com.ets.delivery.application.app.factory.importFile.impl;

import com.alibaba.excel.EasyExcel;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.excellistener.LogisticsExpressImportListener;
import com.ets.delivery.application.common.consts.importFile.ImportFileUploadStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsStatusEnum;
import com.ets.delivery.application.common.dto.logisticsExpress.LogisticsExpressImportDTO;
import com.ets.delivery.application.infra.entity.ImportFileRecord;
import com.ets.delivery.application.infra.entity.ImportLogisticsExpressDetail;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.service.ImportFileRecordService;
import com.ets.delivery.application.infra.service.ImportLogisticsExpressDetailService;
import com.ets.delivery.application.infra.service.LogisticsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Pattern;

@Slf4j
@Component
public class ImportFileLogisticsExpress extends ImportFileBase<ImportLogisticsExpressDetail> {

    @Autowired
    private ImportFileRecordService importFileRecordService;

    @Autowired
    private ImportLogisticsExpressDetailService importLogisticsExpressDetailService;

    @Autowired
    private LogisticsService logisticsService;

    @Override
    public void importFile(MultipartFile file, ImportFileRecord fileRecord) {
        try {
            EasyExcel.read(file.getInputStream(),
                            LogisticsExpressImportDTO.class,
                            new LogisticsExpressImportListener(fileRecord.getBatchNo(), this))
                    .sheet()
                    .headRowNumber(1)
                    .doRead();
        } catch (IOException e) {
            log.error("导入文件失败：{}", e.getLocalizedMessage());
            // 更新导入失败
            fileRecord.setUploadStatus(ImportFileUploadStatusEnum.FAIL.getValue());
            fileRecord.setErrorMsg("导入文件失败");
            fileRecord.setUpdatedAt(LocalDateTime.now());
            importFileRecordService.updateById(fileRecord);
            ToolsHelper.throwException("导入文件失败");
        } catch (Throwable e) {
            fileRecord.setUploadStatus(ImportFileUploadStatusEnum.FAIL.getValue());
            fileRecord.setErrorMsg(e.getLocalizedMessage());
            fileRecord.setUpdatedAt(LocalDateTime.now());
            importFileRecordService.updateById(fileRecord);
            ToolsHelper.throwException(e.getLocalizedMessage());
        }
    }

    @Override
    public void saveImportFileData(String batchNo, List<ImportLogisticsExpressDetail> dataList) {
        // 插入数据
        importLogisticsExpressDetailService.saveBatch(dataList);

        // 更新导入结果
        ImportFileRecord importFileRecord = importFileRecordService.getByBatchNo(batchNo);
        importFileRecord.setTotal(dataList.size());
        importFileRecord.setUploadStatus(ImportFileUploadStatusEnum.SUCCESS.getValue());
        importFileRecord.setUpdatedAt(LocalDateTime.now());
        importFileRecordService.updateById(importFileRecord);
    }

    public String checkImportError(LogisticsExpressImportDTO logisticsExpressImportDTO) {
        String msg = "";
        
        // 必填项检查
        if (StringUtils.isEmpty(logisticsExpressImportDTO.getLogisticsSn())) {
            msg += "发货流水号不能为空；";
        }

        if (StringUtils.isEmpty(logisticsExpressImportDTO.getOrderSn())) {
            msg += "订单号不能为空；";
        }

        if (StringUtils.isEmpty(logisticsExpressImportDTO.getExpressCompany())) {
            msg += "快递公司不能为空；";
        }

        if (StringUtils.isEmpty(logisticsExpressImportDTO.getExpressNumber())) {
            msg += "快递单号不能为空；";
        }

        // 格式校验
        if (StringUtils.isNotEmpty(logisticsExpressImportDTO.getLogisticsSn()) && logisticsExpressImportDTO.getLogisticsSn().length() > 50) {
            msg += "发货流水号长度不能超过50个字符；";
        }

        if (StringUtils.isNotEmpty(logisticsExpressImportDTO.getOrderSn()) && logisticsExpressImportDTO.getOrderSn().length() > 50) {
            msg += "订单号长度不能超过50个字符；";
        }

        if (StringUtils.isNotEmpty(logisticsExpressImportDTO.getExpressCompany()) && logisticsExpressImportDTO.getExpressCompany().length() > 20) {
            msg += "快递公司名称长度不能超过20个字符；";
        }

        if (StringUtils.isNotEmpty(logisticsExpressImportDTO.getExpressNumber()) && logisticsExpressImportDTO.getExpressNumber().length() > 30) {
            msg += "快递单号长度不能超过30个字符；";
        }

        // 快递单号正则校验
        if (StringUtils.isNotEmpty(logisticsExpressImportDTO.getExpressNumber())) {
            String expressNoPattern = "^[A-Za-z0-9]+$";
            if (!Pattern.matches(expressNoPattern, logisticsExpressImportDTO.getExpressNumber())) {
                msg += "快递单号格式错误，只能包含字母和数字；";
            }
        }

        if (StringUtils.isNotEmpty(logisticsExpressImportDTO.getLogisticsSn())) {
            Logistics logistics = logisticsService.getByLogisticsSn(logisticsExpressImportDTO.getLogisticsSn());

            if (ObjectUtils.isEmpty(logistics)) {
                msg += "发货单记录不存在；";
            } else {
                // 发货单状态检查
                if (logistics.getStatus().equals(LogisticsStatusEnum.STATUS_CANCEL.getValue())
                        || logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_CANCEL.getValue())) {
                    msg += "发货单已取消，不允许导入；";
                }
                if (logistics.getStatus().equals(LogisticsStatusEnum.STATUS_NORMAL.getValue())
                        && !logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue())) {
                    msg += "发货单状态为正常且发货状态非处理中，不允许导入；";
                }
                // 操作人一致性校验：仅当存在发货单且其操作人与当前登录用户不一致时，禁止导入
                String currentOperator = RequestHelper.getAdminOperator();
                if (StringUtils.isNotEmpty(logistics.getOperator())
                        && StringUtils.isNotEmpty(currentOperator)
                        && !logistics.getOperator().equals(currentOperator)) {
                    msg += "非同一个操作人不允许导入；";
                }
            }
        }

        return msg;
    }
}