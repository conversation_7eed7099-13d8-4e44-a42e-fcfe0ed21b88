package com.ets.delivery.application.app.business.serial;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.delivery.application.app.disposer.SerialProcessDisposer;
import com.ets.delivery.application.app.factory.serial.SerialFactory;
import com.ets.delivery.application.app.factory.serial.impl.SerialBase;
import com.ets.delivery.application.common.bo.serial.SerialChangeResultBO;
import com.ets.delivery.application.common.bo.serial.SerialProcessAsyncBO;
import com.ets.delivery.application.common.bo.serial.SerialProcessBO;
import com.ets.delivery.application.common.config.queue.express.QueueExpress;
import com.ets.delivery.application.common.consts.serial.SerialStatusEnum;
import com.ets.delivery.application.common.consts.serial.SerialTypeEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaInventoryTypeEnum;
import com.ets.delivery.application.common.dto.serial.AdminSerialListDTO;
import com.ets.delivery.application.common.dto.serial.AdminSerialLogListDTO;
import com.ets.delivery.application.common.dto.serial.AdminStockSerialListDTO;
import com.ets.delivery.application.common.vo.serial.AdminSerialListVO;
import com.ets.delivery.application.common.vo.serial.AdminSerialLogListVO;
import com.ets.delivery.application.common.vo.serial.AdminSerialSelectOptionsVO;
import com.ets.delivery.application.common.vo.serial.AdminStockSerialListVO;
import com.ets.delivery.application.infra.entity.Serial;
import com.ets.delivery.application.infra.entity.SerialLog;
import com.ets.delivery.application.infra.entity.StockGoodsSerial;
import com.ets.delivery.application.infra.service.SerialLogService;
import com.ets.delivery.application.infra.service.SerialService;
import com.ets.delivery.application.infra.service.StockGoodsSerialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;
import org.apache.commons.lang3.StringUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.common.dto.serial.SerialImportDTO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SerialBusiness {

    @Autowired
    private SerialService serialService;

    @Autowired
    private SerialLogService serialLogService;

    @Autowired
    private StockGoodsSerialService stockGoodsSerialService;

    @Autowired
    private QueueExpress queueExpress;

    /**
     * 序列号列表查询
     */
    public IPage<AdminSerialListVO> getList(AdminSerialListDTO dto) {
        IPage<Serial> page = serialService.getPage(dto);
        return page.convert(serial -> {
            AdminSerialListVO vo = new AdminSerialListVO();
            BeanUtil.copyProperties(serial, vo);
            return vo;
        });
    }

    /**
     * 序列号日志列表查询
     */
    public IPage<AdminSerialLogListVO> getLogList(AdminSerialLogListDTO dto) {
        IPage<SerialLog> page = serialLogService.getPage(dto);
        return page.convert(serialLog -> {
            AdminSerialLogListVO vo = new AdminSerialLogListVO();
            BeanUtil.copyProperties(serialLog, vo);
            return vo;
        });
    }

    /**
     * 库存序列号列表查询
     */
    public IPage<AdminStockSerialListVO> getStockSerialList(AdminStockSerialListDTO dto) {
        IPage<StockGoodsSerial> page = stockGoodsSerialService.getPage(dto);
        return page.convert(serial -> {
            AdminStockSerialListVO vo = new AdminStockSerialListVO();
            BeanUtil.copyProperties(serial, vo);
            return vo;
        });
    }

    /**
     * 获取选择项
     */
    public AdminSerialSelectOptionsVO getSelectOptions() {
        AdminSerialSelectOptionsVO vo = new AdminSerialSelectOptionsVO();
        vo.setStatusList(SerialStatusEnum.getSelectOptions());
        vo.setInventoryTypeList(YundaInventoryTypeEnum.getSelectOptions());
        vo.setStorageList(StorageCodeEnum.getSelectOptions());
        return vo;
    }

    public void importFile(MultipartFile file) {
        // 文件校验
        if (file.isEmpty()) {
            ToolsHelper.throwException("文件不存在");
        }

        String filename = file.getOriginalFilename();
        if (StringUtils.isEmpty(filename)) {
            ToolsHelper.throwException("文件名为空");
        }
        if (!filename.endsWith(".xls") && !filename.endsWith(".xlsx")) {
            ToolsHelper.throwException("系统不支持该文件格式，请上传.xls .xlsx格式的文件");
        }

        try {
            // 直接读取Excel并处理序列号数据
            List<SerialImportDTO> importDataList = new ArrayList<>();
            
            EasyExcel.read(file.getInputStream(), SerialImportDTO.class, new ReadListener<SerialImportDTO>() {
                @Override
                public void invoke(SerialImportDTO data, AnalysisContext context) {
                    // 数据校验
                    if (StringUtils.isNotBlank(data.getSerialNo())) {
                        importDataList.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel解析完成，共读取到 {} 条序列号数据", importDataList.size());
                }
            }).sheet().headRowNumber(1).doRead();

            if (importDataList.isEmpty()) {
                ToolsHelper.throwException("Excel文件中没有有效的序列号数据");
            }

            // 直接处理序列号数据
            processSerialImportData(importDataList);
            
        } catch (Exception e) {
            log.error("序列号导入失败，错误：{}", e.getMessage(), e);
            throw new RuntimeException("序列号导入失败：" + e.getMessage());
        }
    }

    /**
     * 处理序列号导入数据
     * @param importDataList 导入的序列号数据列表
     */
    private void processSerialImportData(List<SerialImportDTO> importDataList) {
        log.info("开始处理序列号导入数据，总数量：{}", importDataList.size());

        // 按序列号类型分组处理
        Map<String, List<SerialImportDTO>> groupedByType = importDataList.stream()
                .filter(data -> StringUtils.isNotBlank(data.getSerialType()))
                .collect(Collectors.groupingBy(SerialImportDTO::getSerialType));

        int successCount = 0;
        int failCount = 0;

        for (Map.Entry<String, List<SerialImportDTO>> entry : groupedByType.entrySet()) {
            String serialTypeStr = entry.getKey();
            List<SerialImportDTO> dataList = entry.getValue();

            try {
                // 验证序列号类型
                if (!SerialTypeEnum.isValidValue(serialTypeStr)) {
                    log.warn("无效的序列号类型：{}，跳过 {} 条数据", serialTypeStr, dataList.size());
                    failCount += dataList.size();
                    continue;
                }

                SerialTypeEnum serialType = Arrays.stream(SerialTypeEnum.values())
                        .filter(type -> type.getValue().equals(serialTypeStr))
                        .findFirst()
                        .orElse(null);

                if (serialType == null) {
                    log.warn("找不到序列号类型枚举：{}，跳过 {} 条数据", serialTypeStr, dataList.size());
                    failCount += dataList.size();
                    continue;
                }

                // 按业务单号分组处理
                Map<String, List<SerialImportDTO>> groupedByBusinessSn = dataList.stream()
                        .collect(Collectors.groupingBy(data -> 
                            StringUtils.defaultIfBlank(data.getBusinessSn(), "DEFAULT")));

                for (Map.Entry<String, List<SerialImportDTO>> businessEntry : groupedByBusinessSn.entrySet()) {
                    String businessSn = businessEntry.getKey();
                    List<SerialImportDTO> businessDataList = businessEntry.getValue();

                    try {
                        processImportSerialsByBusinessSn(serialType, businessSn, businessDataList);
                        successCount += businessDataList.size();
                    } catch (Exception e) {
                        log.error("处理业务单号 {} 的序列号时发生异常：{}", businessSn, e.getMessage(), e);
                        failCount += businessDataList.size();
                    }
                }

            } catch (Exception e) {
                log.error("处理序列号类型 {} 的数据时发生异常：{}", serialTypeStr, e.getMessage(), e);
                failCount += dataList.size();
            }
        }

        log.info("序列号导入处理完成，成功：{}，失败：{}", successCount, failCount);
        
        if (failCount > 0) {
            throw new RuntimeException(String.format("序列号导入部分失败，成功：%d，失败：%d", successCount, failCount));
        }
    }

    /**
     * 按业务单号处理导入的序列号
     */
    private void processImportSerialsByBusinessSn(SerialTypeEnum serialType, String businessSn, 
                                                List<SerialImportDTO> dataList) {
        // 提取序列号列表
        List<String> serialList = dataList.stream()
                .map(SerialImportDTO::getSerialNo)
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .collect(Collectors.toList());

        if (serialList.isEmpty()) {
            log.warn("业务单号 {} 没有有效的序列号", businessSn);
            return;
        }

        // 获取第一条记录的信息作为处理参数
        SerialImportDTO firstData = dataList.get(0);
        
        // 构建处理参数
        SerialProcessBO processBO = SerialProcessBO.builder()
                .serialList(serialList)
                .businessSn("DEFAULT".equals(businessSn) ? null : businessSn)
                .storageSku(StringUtils.trimToNull(firstData.getStorageSku()))
                .inventoryType(StringUtils.trimToNull(firstData.getInventoryType()))
                .storageCode(StringUtils.trimToNull(firstData.getStorageCode()))
                .businessSource(StringUtils.trimToNull(firstData.getBusinessSource()))
                .remark(StringUtils.trimToNull(firstData.getRemark()))
                .operator("SYSTEM") // 系统导入
                .operateTime(ToolsHelper.getLocalDateTime(firstData.getOperateTime()))
                .build();

        // 处理序列号
        processSerials(serialType, processBO);

        log.info("成功处理业务单号 {} 的序列号，类型：{}，数量：{}", businessSn, serialType.getDesc(), serialList.size());
    }

    /**
     * 通用序列号处理方法
     * @param serialType 序列号处理类型
     * @param processBO 序列号处理业务对象
     */
    public void processSerials(SerialTypeEnum serialType, SerialProcessBO processBO) {
        SerialBase serial = SerialFactory.create(serialType);
        serial.process(processBO);
    }

    /**
     * 异步处理序列号
     * @param serialType 序列号处理类型
     * @param processBO 序列号处理业务对象
     */
    public void processSerialAsync(SerialTypeEnum serialType, SerialProcessBO processBO) {
        if (processBO == null || processBO.getSerialList() == null || processBO.getSerialList().isEmpty()) {
            log.warn("序列号列表为空，跳过异步处理，业务单号：{}", processBO != null ? processBO.getBusinessSn() : "null");
            return;
        }

        List<String> serialList = processBO.getSerialList();
        int totalSize = serialList.size();
        int batchSize = 1000; // 每批处理1000个序列号

        log.info("开始分批处理序列号，业务单号：{}，总数量：{}，批次大小：{}",
                processBO.getBusinessSn(), totalSize, batchSize);

        // 如果序列号数量小于等于批次大小，直接处理
        if (totalSize <= batchSize) {
            pushSerialProcessTask(serialType, processBO);
            return;
        }

        // 分批处理
        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<String> batchSerialList = serialList.subList(i, endIndex);

            // 创建批次处理对象
            SerialProcessBO batchProcessBO = SerialProcessBO.builder()
                    .serialList(batchSerialList)
                    .businessSn(processBO.getBusinessSn())
                    .storageSku(processBO.getStorageSku())
                    .inventoryType(processBO.getInventoryType())
                    .operator(processBO.getOperator())
                    .operateTime(processBO.getOperateTime())
                    .remark(processBO.getRemark() + " (批次处理 " + (i / batchSize + 1) + "/" + ((totalSize - 1) / batchSize + 1) + ")")
                    .storageCode(processBO.getStorageCode())
                    .businessSource(processBO.getBusinessSource())
                    .targetStatus(processBO.getTargetStatus())
                    .build();

            pushSerialProcessTask(serialType, batchProcessBO);

            log.info("已推送序列号处理批次，业务单号：{}，批次：{}/{}，当前批次数量：{}",
                    processBO.getBusinessSn(), (i / batchSize + 1), ((totalSize - 1) / batchSize + 1), batchSerialList.size());
        }
    }

    /**
     * 推送序列号处理任务到队列
     * @param serialType 序列号处理类型
     * @param processBO 序列号处理业务对象
     */
    private void pushSerialProcessTask(SerialTypeEnum serialType, SerialProcessBO processBO) {
        try {
            SerialProcessAsyncBO asyncBO = new SerialProcessAsyncBO();
            asyncBO.setSerialType(serialType);
            asyncBO.setProcessBO(processBO);
            SerialProcessDisposer disposer = new SerialProcessDisposer(asyncBO);
            queueExpress.push(disposer);

            log.info("序列号处理任务已推送到队列，业务单号：{}，序列号数量：{}",
                    processBO.getBusinessSn(), processBO.getSerialList().size());
        } catch (Exception e) {
            log.error("推送序列号处理任务失败，业务单号：{}，序列号数量：{}，错误：{}",
                    processBO.getBusinessSn(), processBO.getSerialList().size(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检测序列号变更并处理去重
     *
     * @param serialTypeEnum 序列号类型
     * @param businessSn 业务单号
     * @param newSerialList 新的序列号列表
     * @return 序列号变更检测结果
     */
    public SerialChangeResultBO detectSerialChange(SerialTypeEnum serialTypeEnum, String businessSn, List<String> newSerialList) {
        // 查询原有的序列号记录
        List<String> originalSerialList = SerialFactory.create(serialTypeEnum).getSerialListByBusinessSn(businessSn);

        // 处理新序列号列表（去重）
        List<String> processedNewSerialList = newSerialList != null ? newSerialList : new ArrayList<>();
        if (!processedNewSerialList.isEmpty()) {
            processedNewSerialList = processedNewSerialList.stream()
                    .distinct()
                    .collect(Collectors.toList());
        }

        // 比较序列号是否有变动
        boolean serialChanged = !originalSerialList.equals(processedNewSerialList);

        return new SerialChangeResultBO(originalSerialList, processedNewSerialList, serialChanged);
    }

    /**
     * 处理被移除的序列号
     * 为被移除的序列号创建日志记录，表示它们不再与指定业务记录关联
     *
     * @param serialType 序列号类型
     * @param originalSerialList 原有序列号列表
     * @param newSerialList 新序列号列表
     * @param processBO 序列号处理业务对象，包含业务单号、操作人、业务来源、备注和移除状态
     */
    public void processRemovedSerials(SerialTypeEnum serialType, List<String> originalSerialList, List<String> newSerialList,
                                    SerialProcessBO processBO) {
        if (originalSerialList == null || originalSerialList.isEmpty()) {
            return;
        }

        if (newSerialList == null) {
            newSerialList = List.of();
        }

        // 找出被移除的序列号（原有序列号中不在新序列号中的）
        List<String> finalNewSerialList = newSerialList;
        List<String> removedSerialList = originalSerialList.stream()
                .filter(serialNo -> !finalNewSerialList.contains(serialNo))
                .collect(Collectors.toList());

        // 为被移除的序列号创建日志记录
        if (!removedSerialList.isEmpty()) {
            try {
                // 批量创建移除日志记录
                List<SerialLog> removeLogs = removedSerialList.stream()
                        .map(serialNo -> serialLogService.buildLog(serialNo,
                                processBO.getTargetStatus().getValue(),
                                processBO.getBusinessSource(),
                                processBO.getBusinessSn(),
                                processBO.getOperator(),
                                processBO.getOperateTime(),
                                processBO.getRemark(),
                                processBO.getInventoryType()))
                        .collect(Collectors.toList());

                if (!removeLogs.isEmpty()) {
                    serialLogService.saveBatch(removeLogs);
                }
            } catch (Exception e) {
                log.error("处理被移除序列号日志失败，业务单号：{}，移除序列号：{}，错误：{}",
                    processBO.getBusinessSn(), removedSerialList, e.getMessage());
            }
        }

        // 删除旧的序列号记录
        SerialBase serial = SerialFactory.create(serialType);
        serial.deleteSerialBusinessRecord(processBO.getBusinessSn());
    }

}