package com.ets.delivery.application.app.factory.serial.impl;

import com.ets.delivery.application.common.bo.serial.SerialProcessBO;
import com.ets.delivery.application.common.consts.serial.SerialStatusEnum;
import com.ets.delivery.application.infra.entity.StockGoodsInfo;
import com.ets.delivery.application.infra.entity.StockGoodsSerial;
import com.ets.delivery.application.infra.service.StockGoodsInfoService;
import com.ets.delivery.application.infra.service.StockGoodsSerialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 入库序列号处理器
 */
@Slf4j
@Component
public class StockInSerial extends SerialBase {
    
    @Autowired
    private StockGoodsInfoService stockGoodsInfoService;
    
    @Autowired
    private StockGoodsSerialService stockGoodsSerialService;
    

    
    @Override
    protected void setTargetStatus(SerialProcessBO processBO) {
        processBO.setTargetStatus(SerialStatusEnum.IN_STOCK);
    }
    
    @Override
    protected void executeSpecificLogic(SerialProcessResult result, SerialProcessBO processBO) {
        updateStockGoodsSerial(processBO.getSerialList(), processBO.getBusinessSn(), processBO.getStorageSku());
    }
    
    /**
     * 更新StockGoodsSerial表数据
     */
    private void updateStockGoodsSerial(List<String> serialList, String businessSn, String storageSku) {
        try {
            // 查询商品信息
            StockGoodsInfo goodsInfo = stockGoodsInfoService.getByGoodsCode(businessSn, storageSku);
            if (goodsInfo == null) {
                log.warn("未找到商品信息: storageSku={}", storageSku);
                return;
            }
            
            // 查询已存在的记录，避免重复插入
            List<StockGoodsSerial> existingRecords = stockGoodsSerialService.list(
                    stockGoodsSerialService.lambdaQuery()
                            .eq(StockGoodsSerial::getStockSn, businessSn)
                            .eq(StockGoodsSerial::getStockGoodsId, goodsInfo.getId())
                            .in(StockGoodsSerial::getSerialNo, serialList)
                            .getWrapper()
            );

            // 过滤掉已存在的序列号
            List<String> existingSerialNos = existingRecords.stream()
                    .map(StockGoodsSerial::getSerialNo)
                    .toList();

            List<String> filteredSerialList = serialList.stream()
                    .filter(serialNo -> !existingSerialNos.contains(serialNo))
                    .toList();

            if (!filteredSerialList.isEmpty()) {
                // 批量创建StockGoodsSerial记录
                List<StockGoodsSerial> stockGoodsSerials = filteredSerialList.stream()
                        .map(serialNo -> {
                            StockGoodsSerial stockGoodsSerial = new StockGoodsSerial();
                            stockGoodsSerial.setSerialNo(serialNo);
                            stockGoodsSerial.setStockSn(businessSn);
                            stockGoodsSerial.setStockGoodsId(goodsInfo.getId());
                            stockGoodsSerial.setCreatedAt(LocalDateTime.now());
                            stockGoodsSerial.setUpdatedAt(LocalDateTime.now());
                            return stockGoodsSerial;
                        })
                        .collect(Collectors.toList());

                stockGoodsSerialService.saveBatch(stockGoodsSerials);
                log.info("入库序列号记录：过滤掉已存在记录数量：{}，实际插入数量：{}",
                        existingSerialNos.size(), filteredSerialList.size());
            } else if (!existingSerialNos.isEmpty()) {
                log.info("入库序列号记录：所有记录都已存在，跳过插入，数量：{}", existingSerialNos.size());
            }
        } catch (Exception e) {
            log.error("更新StockGoodsSerial表数据失败: businessSn={}, storageSku={}", businessSn, storageSku, e);
        }
    }
}