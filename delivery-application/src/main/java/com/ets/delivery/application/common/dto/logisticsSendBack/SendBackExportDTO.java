package com.ets.delivery.application.common.dto.logisticsSendBack;

import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Data
public class SendBackExportDTO {
    @NotBlank(message = "仓库编码不能为空")
    private String storageCode;

    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;

    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;

    /**
     * 校验仓库编码是否为有效的枚举值
     * @return 如果仓库编码有效返回true，否则返回false
     */
    @AssertTrue(message = "仓库编码不合法")
    public boolean isValidStorageCode() {
        return StorageCodeEnum.isValidValue(this.storageCode);
    }

    /**
     * 校验日期范围是否合理
     * @return 如果日期范围合理返回true，否则返回false
     */
    @AssertTrue(message = "结束日期必须大于或等于开始日期")
    public boolean isValidDateRange() {
        if (startDate == null || endDate == null) {
            return true; // 让 @NotNull 处理空值校验
        }
        return !endDate.isBefore(startDate);
    }
}