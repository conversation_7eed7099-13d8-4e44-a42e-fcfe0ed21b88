package com.ets.delivery.application.common.advice;

import com.ets.base.feign.feign.CosFeign;
import com.ets.base.feign.request.CosGetSignDTO;
import com.ets.base.feign.response.PrivateCosVO;
import com.ets.common.JsonResult;
import com.ets.delivery.application.common.annotation.CosSignWithWatermarkAnnotation;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.starter.advice.AbstractCosSignResponseBodyAdvice;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.web.bind.annotation.ControllerAdvice;
import java.util.HashMap;
import java.util.Map;

/**
 * COS图片签名加水印响应体处理器
 * 继承AbstractCosSignResponseBodyAdvice的功能，并添加水印参数处理
 */
@ControllerAdvice
@Slf4j
public class CosSignWithWatermarkResponseBodyAdvice extends AbstractCosSignResponseBodyAdvice {

    @Autowired
    private CosFeign cosFeign;

    @Autowired
    private DeliveryConfig deliveryConfig;

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        // 检查方法是否有CosSignWithWatermarkAnnotation注解
        return returnType.hasMethodAnnotation(CosSignWithWatermarkAnnotation.class);
    }

    @Override
    protected HashMap<String, String> getSignedUrls(HashMap<String, String> matchedUrls) {
        // 先添加水印参数
        HashMap<String, String> watermarkUrls = new HashMap<>();
        for (Map.Entry<String, String> entry : matchedUrls.entrySet()) {
            String originalUrl = entry.getValue();
            String watermarkUrl = appendWatermarkParams(originalUrl);
            watermarkUrls.put(entry.getKey(), watermarkUrl);
        }

        // 然后获取签名URL
        CosGetSignDTO dto = new CosGetSignDTO();
        dto.setUrls(watermarkUrls);

        JsonResult<PrivateCosVO> result = cosFeign.getSignUrl(dto);

        return result.getDataWithCheckError().getUrls();
    }

    /**
     * 为URL添加水印参数
     */
    private String appendWatermarkParams(String url) {
        String watermarkBase64 = deliveryConfig.getWatermarkBase64();
        if (url == null || watermarkBase64 == null || watermarkBase64.isEmpty()) {
            return url;
        }

        // 按照格式拼接水印参数: watermark/1/image/{watermarkBase64}/batch/1
        String watermarkParams = "watermark/1/image/" + watermarkBase64 + "/batch/1";

        // 如果URL包含查询参数，则在末尾追加水印参数，否则用?拼接
        if (url.contains("?")) {
            return url + "&" + watermarkParams;
        } else {
            return url + "?" + watermarkParams;
        }
    }
}
