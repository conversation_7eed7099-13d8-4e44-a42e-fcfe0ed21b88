package com.ets.delivery.application.app.excellistener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.importFile.impl.ImportFileLogisticsExpress;
import com.ets.delivery.application.common.consts.importFile.ImportResultLevelEnum;
import com.ets.delivery.application.common.dto.logisticsExpress.LogisticsExpressImportDTO;
import com.ets.delivery.application.infra.entity.ImportLogisticsExpressDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class LogisticsExpressImportListener extends AnalysisEventListener<LogisticsExpressImportDTO> {

    private List<ImportLogisticsExpressDetail> dataList;

    private final String batchNo;

    private ImportFileLogisticsExpress importFileLogisticsExpress;

    public LogisticsExpressImportListener(String batchNo, ImportFileLogisticsExpress importFileLogisticsExpress) {
        this.batchNo = batchNo;
        this.importFileLogisticsExpress = importFileLogisticsExpress;
        this.dataList = new ArrayList<>();
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("headMap:{}", headMap);

        // 校验表头
        Collection<String> excelHead = headMap.values();
        List<String> head = Arrays.asList("发货流水号", "订单号", "快递公司", "快递单号");
        head.forEach(key -> {
            if (!excelHead.contains(key)) {
                ToolsHelper.throwException("上传文件列项与模板不一致");
            }
        });

        // 行数
        Integer total = context.readSheetHolder().getApproximateTotalRowNumber();
        if (total == 1 || total > 1001) {
            ToolsHelper.throwException("上传文件超过行数限制，请上传最小为1条最大为1000条数据");
        }
    }

    @Override
    public void invoke(LogisticsExpressImportDTO logisticsExpressImportDTO, AnalysisContext analysisContext) {
        // 清洗数据
        if (ObjectUtils.isEmpty(logisticsExpressImportDTO)) {
            return;
        }

        // 参数去空格
        logisticsExpressImportDTO.setLogisticsSn(StringUtils.trimToEmpty(logisticsExpressImportDTO.getLogisticsSn()));
        logisticsExpressImportDTO.setOrderSn(StringUtils.trimToEmpty(logisticsExpressImportDTO.getOrderSn()));
        logisticsExpressImportDTO.setExpressCompany(StringUtils.trimToEmpty(logisticsExpressImportDTO.getExpressCompany()));
        logisticsExpressImportDTO.setExpressNumber(StringUtils.trimToEmpty(logisticsExpressImportDTO.getExpressNumber()));

        // 组装数据
        ImportLogisticsExpressDetail detail = new ImportLogisticsExpressDetail();
        detail.setBatchNo(batchNo);
        detail.setLogisticsSn(logisticsExpressImportDTO.getLogisticsSn());
        detail.setOrderSn(logisticsExpressImportDTO.getOrderSn());
        detail.setExpressCompany(logisticsExpressImportDTO.getExpressCompany());
        detail.setExpressNumber(logisticsExpressImportDTO.getExpressNumber());
        detail.setResultLevel(ImportResultLevelEnum.LEVEL_NORMAL.getValue()); // 正常
        detail.setResultMsg("");
        detail.setStatus(0); // 0-正常
        detail.setRecordStatus(0); // 0-初始化
        detail.setCreatedAt(LocalDateTime.now());
        detail.setUpdatedAt(LocalDateTime.now());

        // 检查失败项目
        String errorMsg = importFileLogisticsExpress.checkImportError(logisticsExpressImportDTO);
        if (StringUtils.isNotEmpty(errorMsg)) {
            detail.setResultLevel(ImportResultLevelEnum.LEVEL_ERROR.getValue()); // 错误
            detail.setResultMsg(errorMsg);
        }

        // 缓存
        dataList.add(detail);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 查重标记
        Map<String, Long> counts = dataList.stream()
                .filter(detail -> StringUtils.isNotEmpty(detail.getLogisticsSn()))
                .collect(Collectors.groupingBy(ImportLogisticsExpressDetail::getLogisticsSn, Collectors.counting()));

        dataList = dataList.stream()
                .peek(detail -> {
                    if (counts.get(detail.getLogisticsSn()) > 1) {
                        detail.setResultMsg(detail.getResultMsg() + "文件中存在相同的发货流水号;");
                        // 正常需要改成警告
                        if (detail.getResultLevel().equals(ImportResultLevelEnum.LEVEL_NORMAL.getValue())) {
                            detail.setResultLevel(ImportResultLevelEnum.LEVEL_WARNING.getValue());
                        }
                    }
                })
                .collect(Collectors.toList());

        log.info("【发货单导入】导入成功 批次号：{} 条数：{}", batchNo, dataList.size());
        importFileLogisticsExpress.saveImportFileData(batchNo, dataList);
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        log.error("【发货单导入】导入失败：", exception);
        throw exception;
    }
}