package com.ets.delivery.application.common.vo.serial;

import com.ets.delivery.application.common.consts.serial.SerialStatusEnum;
import com.ets.delivery.application.common.consts.yunda.YundaInventoryTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AdminSerialLogListVO {

    private Integer id;

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 状态[1-在库 2-出库 3-发货 4-退回]
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusStr;

    /**
     * 库存类型
     */
    private String inventoryType;

    /**
     * 库存类型描述
     */
    private String inventoryTypeStr;

    /**
     * 业务来源
     */
    private String businessSource;

    /**
     * 业务单号
     */
    private String businessSn;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime operateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    public String getStatusStr() {
        return SerialStatusEnum.getDescByValue(status);
    }

    public String getInventoryTypeStr() {
        return YundaInventoryTypeEnum.getDescByValue(inventoryType);
    }
}