package com.ets.delivery.application.common.vo.logisticsSendBack;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

@Data
@HeadRowHeight(20)
@ContentRowHeight(15)
@ColumnWidth(25)
public class SendBackExportExcelVO {
    @ExcelProperty("日期")
    private String date;
    
    @ExcelProperty("订单号")
    private String orderSn;
    
    @ExcelProperty("快递公司")
    private String expressCorp;
    
    @ExcelProperty("快递单号")
    private String expressNumber;
    
    @ExcelProperty("手机号码")
    private String sendPhone;
    
    @ExcelProperty("仓储")
    private String storageCode;
    
    @ExcelProperty("商品名称")
    private String goodsName;
}