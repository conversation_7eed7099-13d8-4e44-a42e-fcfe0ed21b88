package com.ets.delivery.application.common.dto.logistics;

import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class LogisticsExportDTO {
    @NotBlank(message = "仓库编码不能为空")
    private String storageCode;

    // 允许为空，业务中会给默认值；若传入则限定范围
    @Min(value = 1, message = "导出条数至少为1")
    @Max(value = 10000, message = "导出条数最多10000")
    private Integer count;

    /**
     * 校验仓库编码是否为有效的枚举值
     * @return 如果仓库编码有效返回true，否则返回false
     */
    @AssertTrue(message = "仓库编码不合法")
    public boolean isValidStorageCode() {
        return StorageCodeEnum.isValidValue(this.storageCode);
    }
}