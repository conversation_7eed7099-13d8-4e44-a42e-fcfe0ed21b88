package com.ets.delivery.application.common.consts.importFile;

import com.ets.delivery.application.app.factory.importFile.impl.ImportFileBase;
import com.ets.delivery.application.app.factory.importFile.impl.ImportFileLogistics;
import com.ets.delivery.application.app.factory.importFile.impl.ImportFileLogisticsExpress;
import com.ets.delivery.application.app.factory.importFile.impl.ImportFileRejectExpress;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ImportFileImportTypeEnum {

    MANUAL_LOGISTICS_IMPORT("logisticsImport", ImportFileLogistics.class, "手动发货导入"),
    REJECT_EXPRESS_IMPORT("rejectExpressImport", ImportFileRejectExpress.class, "拒收快递导入"),
    LOGISTICS_EXPRESS_IMPORT("logisticsExpressImport", ImportFileLogisticsExpress.class, "发货快递单导入");

    private final String type;
    private final Class<? extends ImportFileBase> job;
    private final String desc;
    public static final List<String> list;

    static {
        list = Arrays.stream(ImportFileImportTypeEnum.values()).map(ImportFileImportTypeEnum::getType).collect(Collectors.toList());
    }
}
